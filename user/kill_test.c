#include "kernel/types.h"
#include "kernel/stat.h"
#include "user/user.h"

int
main(int argc, char *argv[])
{
  int pid = fork();

  if(pid < 0){
    printf("fork failed\n");
    exit(1);
  }

  if(pid == 0){
    // child process - sleep forever
    while(1){
      sleep(10);
    }
  } else {
    // parent process - kill child
    sleep(1);
    printf("parent: killing child %d\n", pid);
    kill(pid);
    wait(0, 0);
    printf("parent: child killed\n");
    exit(0);
  }
}
