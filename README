xv6 is a re-implementation of <PERSON>'s and <PERSON>'s Unix
Version 6 (v6).  xv6 loosely follows the structure and style of v6,
but is implemented for a modern RISC-V multiprocessor using ANSI C.

ACKNOWLEDGMENTS

xv6 is inspired by <PERSON>'s Commentary on UNIX 6th Edition (Peer
to Peer Communications; ISBN: 1-57398-013-7; 1st edition (June 14,
2000)). See also https://pdos.csail.mit.edu/6.828/, which
provides pointers to on-line resources for v6.

The following people have made contributions: <PERSON> (context switching,
locking), <PERSON> (MP), <PERSON> (MP), <PERSON><PERSON><PERSON>, and <PERSON>.

We are also grateful for the bug reports and patches contributed by
<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>,
<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>yalz800, , <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>,<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>,
<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>,
<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>,
<PERSON>, <PERSON><PERSON>, <PERSON>dar <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> <PERSON>, t<PERSON>, <PERSON> <PERSON>bal, <PERSON> <PERSON>mey,
<PERSON> <PERSON>, <PERSON>, <PERSON>, <PERSON>ii<PERSON> <PERSON>nabe, <PERSON>
<PERSON><PERSON>k, wxdao, <PERSON> <PERSON>, <PERSON> <PERSON>, Icenowy Zheng, and Z<PERSON>.

<PERSON> code in the files that constitute xv6 is
<PERSON> 2006-2020 <PERSON> Kaashoek, <PERSON> <PERSON>, and Russ <PERSON>.

ERROR REP<PERSON>TS

Please send errors and suggestions to Frans Kaashoek and Robert Morris
(kaashoek,<EMAIL>). The main purpose of xv6 is as a teaching
operating system for MIT's 6.S081, so we are more interested in
simplifications and clarifications than new features.

BUILDING AND RUNNING XV6

You will need a RISC-V "newlib" tool chain from
https://github.com/riscv/riscv-gnu-toolchain, and qemu compiled for
riscv64-softmmu. Once they are installed, and in your shell
search path, you can run "make qemu".
