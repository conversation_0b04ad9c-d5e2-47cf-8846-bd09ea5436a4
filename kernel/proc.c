#include "types.h"
#include "param.h"
#include "memlayout.h"
#include "riscv.h"
#include "spinlock.h"
#include "proc.h"
#include "defs.h"

struct cpu cpus[NCPU];

struct proc proc[NPROC];

struct proc *initproc;

int nextpid = 1;
struct spinlock pid_lock;

// Scheduling queue and related variables
struct spinlock sched_lock;
struct sched_queue runqueue = {.size = 0};
uint total_weight = 0;

extern void forkret(void);
static void wakeup1(struct proc *chan);
static void freeproc(struct proc *p);
void update_state(struct proc *p, enum procstate newstate);
int get_weight(int nice);

// Min-heap operations for scheduling queue
static void heap_swap(int i, int j);
static void heap_bubble_up(int index);
static void heap_bubble_down(int index);
static void heap_insert(struct proc *p);
static struct proc *heap_extract_min(void);
static void heap_remove(struct proc *p);
static int heap_find(struct proc *p);

// Legacy functions (will be replaced)
static uint64 get_min_vruntime(void);
static void add_to_sched_queue(struct proc *p);
static void remove_from_sched_queue(struct proc *p);

extern char trampoline[];  // trampoline.S

// initialize the proc table at boot time.
void procinit(void) {
  struct proc *p;

  initlock(&pid_lock, "nextpid");
  initlock(&sched_lock, "sched");

  // Initialize CPU time tracking
  for (int i = 0; i < NCPU; i++) {
    cpus[i].cpu_time = 0;
  }

  for (p = proc; p < &proc[NPROC]; p++) {
    initlock(&p->lock, "proc");

    // Allocate a page for the process's kernel stack.
    // Map it high in memory, followed by an invalid
    // guard page.
    char *pa = kalloc();
    if (pa == 0) panic("kalloc");
    uint64 va = KSTACK((int)(p - proc));
    kvmmap(va, (uint64)pa, PGSIZE, PTE_R | PTE_W);
    p->kstack = va;
  }
  kvminithart();
}

// Must be called with interrupts disabled,
// to prevent race with process being moved
// to a different CPU.
int cpuid() {
  int id = r_tp();
  return id;
}

// Return this CPU's cpu struct.
// Interrupts must be disabled.
struct cpu *mycpu(void) {
  int id = cpuid();
  struct cpu *c = &cpus[id];
  return c;
}

// Return the current struct proc *, or zero if none.
struct proc *myproc(void) {
  push_off();
  struct cpu *c = mycpu();
  struct proc *p = c->proc;
  pop_off();
  return p;
}

int allocpid() {
  int pid;

  acquire(&pid_lock);
  pid = nextpid;
  nextpid = nextpid + 1;
  release(&pid_lock);

  return pid;
}

// Look in the process table for an UNUSED proc.
// If found, initialize state required to run in the kernel,
// and return with p->lock held.
// If there are no free procs, or a memory allocation fails, return 0.
static struct proc *allocproc(void) {
  struct proc *p;

  for (p = proc; p < &proc[NPROC]; p++) {
    acquire(&p->lock);
    if (p->state == UNUSED) {
      goto found;
    } else {
      release(&p->lock);
    }
  }
  return 0;

found:
  p->pid = allocpid();

  // Allocate a trapframe page.
  if ((p->trapframe = (struct trapframe *)kalloc()) == 0) {
    release(&p->lock);
    return 0;
  }

  // An empty user page table.
  p->pagetable = proc_pagetable(p);
  if (p->pagetable == 0) {
    freeproc(p);
    release(&p->lock);
    return 0;
  }

  // Set up new context to start executing at forkret,
  // which returns to user space.
  memset(&p->context, 0, sizeof(p->context));
  p->context.ra = (uint64)forkret;
  p->context.sp = p->kstack + PGSIZE;

  // Initialize time tracking fields
  p->running_time = 0;
  p->runnable_time = 0;
  p->sleep_time = 0;
  p->last_timestamp = ticks;

  // Initialize scheduling fields
  p->nice = 3;              // Default nice value
  p->vruntime = 0;
  p->time_slice = 0;
  p->time_slice_start = 0;
  p->preempt_pending = 0;
  p->in_sched_queue = 0;

  return p;
}

// free a proc structure and the data hanging from it,
// including user pages.
// p->lock must be held.
static void freeproc(struct proc *p) {
  if (p->trapframe) kfree((void *)p->trapframe);
  p->trapframe = 0;
  if (p->pagetable) proc_freepagetable(p->pagetable, p->sz);
  p->pagetable = 0;
  p->sz = 0;
  p->pid = 0;
  p->parent = 0;
  p->name[0] = 0;
  p->chan = 0;
  p->killed = 0;
  p->xstate = 0;
  p->state = UNUSED;
}

// Create a user page table for a given process,
// with no user memory, but with trampoline pages.
pagetable_t proc_pagetable(struct proc *p) {
  pagetable_t pagetable;

  // An empty page table.
  pagetable = uvmcreate();
  if (pagetable == 0) return 0;

  // map the trampoline code (for system call return)
  // at the highest user virtual address.
  // only the supervisor uses it, on the way
  // to/from user space, so not PTE_U.
  if (mappages(pagetable, TRAMPOLINE, PGSIZE, (uint64)trampoline, PTE_R | PTE_X) < 0) {
    uvmfree(pagetable, 0);
    return 0;
  }

  // map the trapframe just below TRAMPOLINE, for trampoline.S.
  if (mappages(pagetable, TRAPFRAME, PGSIZE, (uint64)(p->trapframe), PTE_R | PTE_W) < 0) {
    uvmunmap(pagetable, TRAMPOLINE, 1, 0);
    uvmfree(pagetable, 0);
    return 0;
  }

  return pagetable;
}

// Free a process's page table, and free the
// physical memory it refers to.
void proc_freepagetable(pagetable_t pagetable, uint64 sz) {
  uvmunmap(pagetable, TRAMPOLINE, 1, 0);
  uvmunmap(pagetable, TRAPFRAME, 1, 0);
  uvmfree(pagetable, sz);
}

// a user program that calls exec("/init")
// od -t xC initcode
uchar initcode[] = {0x17, 0x05, 0x00, 0x00, 0x13, 0x05, 0x45, 0x02, 0x97, 0x05, 0x00, 0x00, 0x93,
                    0x85, 0x35, 0x02, 0x93, 0x08, 0x70, 0x00, 0x73, 0x00, 0x00, 0x00, 0x93, 0x08,
                    0x20, 0x00, 0x73, 0x00, 0x00, 0x00, 0xef, 0xf0, 0x9f, 0xff, 0x2f, 0x69, 0x6e,
                    0x69, 0x74, 0x00, 0x00, 0x24, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};

// Set up first user process.
void userinit(void) {
  struct proc *p;

  p = allocproc();
  initproc = p;

  // allocate one user page and copy init's instructions
  // and data into it.
  uvminit(p->pagetable, initcode, sizeof(initcode));
  p->sz = PGSIZE;

  // prepare for the very first "return" from kernel to user.
  p->trapframe->epc = 0;      // user program counter
  p->trapframe->sp = PGSIZE;  // user stack pointer

  safestrcpy(p->name, "initcode", sizeof(p->name));
  p->cwd = namei("/");

  update_state(p, RUNNABLE);

  release(&p->lock);
}

// Grow or shrink user memory by n bytes.
// Return 0 on success, -1 on failure.
int growproc(int n) {
  uint sz;
  struct proc *p = myproc();

  sz = p->sz;
  if (n > 0) {
    if ((sz = uvmalloc(p->pagetable, sz, sz + n)) == 0) {
      return -1;
    }
  } else if (n < 0) {
    sz = uvmdealloc(p->pagetable, sz, sz + n);
  }
  p->sz = sz;
  return 0;
}

// Create a new process, copying the parent.
// Sets up child kernel stack to return as if from fork() system call.
int fork(void) {
  int i, pid;
  struct proc *np;
  struct proc *p = myproc();

  // Allocate process.
  if ((np = allocproc()) == 0) {
    return -1;
  }

  // Copy user memory from parent to child.
  if (uvmcopy(p->pagetable, np->pagetable, p->sz) < 0) {
    freeproc(np);
    release(&np->lock);
    return -1;
  }
  np->sz = p->sz;

  np->parent = p;

  // copy saved user registers.
  *(np->trapframe) = *(p->trapframe);

  // Cause fork to return 0 in the child.
  np->trapframe->a0 = 0;

  // increment reference counts on open file descriptors.
  for (i = 0; i < NOFILE; i++)
    if (p->ofile[i]) np->ofile[i] = filedup(p->ofile[i]);
  np->cwd = idup(p->cwd);

  safestrcpy(np->name, p->name, sizeof(p->name));

  pid = np->pid;

  update_state(np, RUNNABLE);

  release(&np->lock);

  return pid;
}

// Pass p's abandoned children to init.
// Caller must hold p->lock.
void reparent(struct proc *p) {
  struct proc *pp;

  for (pp = proc; pp < &proc[NPROC]; pp++) {
    // this code uses pp->parent without holding pp->lock.
    // acquiring the lock first could cause a deadlock
    // if pp or a child of pp were also in exit()
    // and about to try to lock p.
    if (pp->parent == p) {
      // pp->parent can't change between the check and the acquire()
      // because only the parent changes it, and we're the parent.
      acquire(&pp->lock);
      pp->parent = initproc;
      // we should wake up init here, but that would require
      // initproc->lock, which would be a deadlock, since we hold
      // the lock on one of init's children (pp). this is why
      // exit() always wakes init (before acquiring any locks).
      release(&pp->lock);
    }
  }
}

// Exit the current process.  Does not return.
// An exited process remains in the zombie state
// until its parent calls wait().
void exit(int status) {
  struct proc *p = myproc();

  if (p == initproc) panic("init exiting");

  // Close all open files.
  for (int fd = 0; fd < NOFILE; fd++) {
    if (p->ofile[fd]) {
      struct file *f = p->ofile[fd];
      fileclose(f);
      p->ofile[fd] = 0;
    }
  }

  begin_op();
  iput(p->cwd);
  end_op();
  p->cwd = 0;

  // we might re-parent a child to init. we can't be precise about
  // waking up init, since we can't acquire its lock once we've
  // acquired any other proc lock. so wake up init whether that's
  // necessary or not. init may miss this wakeup, but that seems
  // harmless.
  acquire(&initproc->lock);
  wakeup1(initproc);
  release(&initproc->lock);

  // grab a copy of p->parent, to ensure that we unlock the same
  // parent we locked. in case our parent gives us away to init while
  // we're waiting for the parent lock. we may then race with an
  // exiting parent, but the result will be a harmless spurious wakeup
  // to a dead or wrong process; proc structs are never re-allocated
  // as anything else.
  acquire(&p->lock);
  struct proc *original_parent = p->parent;
  release(&p->lock);

  // we need the parent's lock in order to wake it up from wait().
  // the parent-then-child rule says we have to lock it first.
  acquire(&original_parent->lock);

  acquire(&p->lock);

  // Give any children to init.
  reparent(p);

  // Parent might be sleeping in wait().
  wakeup1(original_parent);

  p->xstate = status;
  update_state(p, ZOMBIE);

  release(&original_parent->lock);

  // Jump into the scheduler, never to return.
  sched();
  panic("zombie exit");
}

// Wait for a child process to exit and return its pid.
// Return -1 if this process has no children.
int wait(uint64 addr) {
  struct proc *np;
  int havekids, pid;
  struct proc *p = myproc();

  // hold p->lock for the whole time to avoid lost
  // wakeups from a child's exit().
  acquire(&p->lock);

  for (;;) {
    // Scan through table looking for exited children.
    havekids = 0;
    for (np = proc; np < &proc[NPROC]; np++) {
      // this code uses np->parent without holding np->lock.
      // acquiring the lock first would cause a deadlock,
      // since np might be an ancestor, and we already hold p->lock.
      if (np->parent == p) {
        // np->parent can't change between the check and the acquire()
        // because only the parent changes it, and we're the parent.
        acquire(&np->lock);
        havekids = 1;
        if (np->state == ZOMBIE) {
          // Found one.
          pid = np->pid;
          if (addr != 0 && copyout(p->pagetable, addr, (char *)&np->xstate, sizeof(np->xstate)) < 0) {
            release(&np->lock);
            release(&p->lock);
            return -1;
          }
          freeproc(np);
          release(&np->lock);
          release(&p->lock);
          return pid;
        }
        release(&np->lock);
      }
    }

    // No point waiting if we don't have any children.
    if (!havekids || p->killed) {
      release(&p->lock);
      return -1;
    }

    // Wait for a child to exit.
    sleep(p, &p->lock);  // DOC: wait-sleep
  }
}

// Per-CPU process scheduler.
// Each CPU calls scheduler() after setting itself up.
// Scheduler never returns.  It loops, doing:
//  - choose a process to run.
//  - swtch to start running that process.
//  - eventually that process transfers control
//    via swtch back to the scheduler.
void scheduler(void) {
  struct proc *p;
  struct cpu *c = mycpu();

  c->proc = 0;
  for (;;) {
    // Avoid deadlock by ensuring that devices can interrupt.
    intr_on();

    // Find and lock the process with minimum vruntime
    // Use a safer approach: peek first, then lock process, then extract
    acquire(&sched_lock);

    struct proc *min_p = 0;
    if (runqueue.size > 0) {
      min_p = runqueue.heap[0];  // Peek at minimum process
    }

    release(&sched_lock);

    // Now safely acquire the process lock and re-check
    if (min_p != 0) {
      acquire(&min_p->lock);

      // Re-acquire sched_lock to safely extract the process
      acquire(&sched_lock);

      // Double-check the process is still at the root and runnable
      if (runqueue.size > 0 && runqueue.heap[0] == min_p &&
          min_p->state == RUNNABLE && min_p->in_sched_queue) {

        // Extract the process from heap
        heap_extract_min();

        // Calculate scheduling parameters
        int weight = get_weight(min_p->nice);
        int base_latency = 20;
        int nr_running = runqueue.size + 1;  // +1 for the extracted process
        uint time_slice = 10;  // Default time slice

        if (total_weight > 0 && nr_running > 0) {
          // time_slice = (base_latency * weight / total_weight) * log_factor
          int log_factor = 1;
          if (nr_running > 4) log_factor = 2;
          if (nr_running > 8) log_factor = 3;

          time_slice = (base_latency * weight * log_factor) / total_weight;

          // Clamp time slice to reasonable bounds
          if (time_slice < 3) time_slice = 3;
          if (time_slice > 100) time_slice = 100;
        }

        min_p->time_slice = time_slice;
        min_p->time_slice_start = ticks;

        release(&sched_lock);

        // Switch to chosen process
        uint start_time = ticks;
        update_state(min_p, RUNNING);
        c->proc = min_p;
        swtch(&c->context, &min_p->context);

        // Process is done running for now
        c->proc = 0;

        // Update CPU time for the time this process ran
        uint end_time = ticks;
        c->cpu_time += (end_time - start_time);

      } else {
        // Process is no longer at root or not runnable, put it back
        release(&sched_lock);
        min_p = 0;  // Don't run this process
      }

      release(&min_p->lock);
    }

    if (min_p == 0) {
      // No runnable process, wait for interrupt
      // Use WFI (Wait For Interrupt) to save power
      intr_on();
      asm volatile("wfi");
    }
  }
}

// Switch to scheduler.  Must hold only p->lock
// and have changed proc->state. Saves and restores
// intena because intena is a property of this
// kernel thread, not this CPU. It should
// be proc->intena and proc->noff, but that would
// break in the few places where a lock is held but
// there's no process.
void sched(void) {
  int intena;
  struct proc *p = myproc();

  if (!holding(&p->lock)) panic("sched p->lock");
  if (mycpu()->noff != 1) panic("sched locks");
  if (p->state == RUNNING) panic("sched running");
  if (intr_get()) panic("sched interruptible");

  intena = mycpu()->intena;
  swtch(&p->context, &mycpu()->context);
  mycpu()->intena = intena;
}

// Give up the CPU for one scheduling round.
void yield(void) {
  struct proc *p = myproc();
  acquire(&p->lock);
  update_state(p, RUNNABLE);
  sched();
  release(&p->lock);
}

// A fork child's very first scheduling by scheduler()
// will swtch to forkret.
void forkret(void) {
  static int first = 1;

  // Still holding p->lock from scheduler.
  release(&myproc()->lock);

  if (first) {
    // File system initialization must be run in the context of a
    // regular process (e.g., because it calls sleep), and thus cannot
    // be run from main().
    first = 0;
    fsinit(ROOTDEV);
  }

  usertrapret();
}

// Atomically release lock and sleep on chan.
// Reacquires lock when awakened.
void sleep(void *chan, struct spinlock *lk) {
  struct proc *p = myproc();

  // Must acquire p->lock in order to
  // change p->state and then call sched.
  // Once we hold p->lock, we can be
  // guaranteed that we won't miss any wakeup
  // (wakeup locks p->lock),
  // so it's okay to release lk.
  if (lk != &p->lock) {  // DOC: sleeplock0
    acquire(&p->lock);   // DOC: sleeplock1
    release(lk);
  }

  // Go to sleep.
  p->chan = chan;
  update_state(p, SLEEPING);

  sched();

  // Tidy up.
  p->chan = 0;

  // Reacquire original lock.
  if (lk != &p->lock) {
    release(&p->lock);
    acquire(lk);
  }
}

// Wake up all processes sleeping on chan.
// Must be called without any p->lock.
void wakeup(void *chan) {
  struct proc *p;

  for (p = proc; p < &proc[NPROC]; p++) {
    acquire(&p->lock);
    if (p->state == SLEEPING && p->chan == chan) {
      update_state(p, RUNNABLE);
    }
    release(&p->lock);
  }
}

// Wake up p if it is sleeping in wait(); used by exit().
// Caller must hold p->lock.
static void wakeup1(struct proc *p) {
  if (!holding(&p->lock)) panic("wakeup1");
  if (p->chan == p && p->state == SLEEPING) {
    update_state(p, RUNNABLE);
  }
}

// Kill the process with the given pid.
// The victim won't exit until it tries to return
// to user space (see usertrap() in trap.c).
int kill(int pid) {
  struct proc *p;

  for (p = proc; p < &proc[NPROC]; p++) {
    acquire(&p->lock);
    if (p->pid == pid) {
      p->killed = 1;
      if (p->state == SLEEPING) {
        // Wake process from sleep().
        update_state(p, RUNNABLE);
      }
      release(&p->lock);
      return 0;
    }
    release(&p->lock);
  }
  return -1;
}

// Copy to either a user address, or kernel address,
// depending on usr_dst.
// Returns 0 on success, -1 on error.
int either_copyout(int user_dst, uint64 dst, void *src, uint64 len) {
  struct proc *p = myproc();
  if (user_dst) {
    return copyout(p->pagetable, dst, src, len);
  } else {
    memmove((char *)dst, src, len);
    return 0;
  }
}

// Copy from either a user address, or kernel address,
// depending on usr_src.
// Returns 0 on success, -1 on error.
int either_copyin(void *dst, int user_src, uint64 src, uint64 len) {
  struct proc *p = myproc();
  if (user_src) {
    return copyin(p->pagetable, dst, src, len);
  } else {
    memmove(dst, (char *)src, len);
    return 0;
  }
}

// Print a process listing to console.  For debugging.
// Runs when user types ^P on console.
// No lock to avoid wedging a stuck machine further.
void procdump(void) {
  static char *states[] = {
      [UNUSED] = "unused", [SLEEPING] = "sleep ", [RUNNABLE] = "runble", [RUNNING] = "run   ", [ZOMBIE] = "zombie"};
  struct proc *p;
  char *state;

  printf("\n");
  for (p = proc; p < &proc[NPROC]; p++) {
    if (p->state == UNUSED) continue;
    if (p->state >= 0 && p->state < NELEM(states) && states[p->state])
      state = states[p->state];
    else
      state = "???";
    printf("%d %s %s", p->pid, state, p->name);
    printf("\n");
  }
}

// Helper function to calculate process weight from nice value
// Weight = 4 - nice, so nice=1 -> weight=3, nice=2 -> weight=2, nice=3 -> weight=1
int get_weight(int nice) {
  return 4 - nice;
}

// Find the minimum vruntime among all RUNNABLE processes
// Must be called with sched_lock held
static uint64 get_min_vruntime(void) {
  uint64 min_vrt = 0;
  int found = 0;
  struct proc *p;

  for (p = proc; p < &proc[NPROC]; p++) {
    if (p->in_sched_queue) {
      if (!found || p->vruntime < min_vrt) {
        min_vrt = p->vruntime;
        found = 1;
      }
    }
  }

  return min_vrt;
}

// Find the maximum vruntime among all RUNNABLE processes
// Must be called with sched_lock held
static uint64 get_max_vruntime(void) {
  uint64 max_vrt = 0;
  int found = 0;
  struct proc *p;

  for (p = proc; p < &proc[NPROC]; p++) {
    if (p->in_sched_queue) {
      if (!found || p->vruntime > max_vrt) {
        max_vrt = p->vruntime;
        found = 1;
      }
    }
  }

  return max_vrt;
}

// Add process to scheduling queue
// Must be called with both sched_lock and p->lock held
static void add_to_sched_queue(struct proc *p) {
  if (p->in_sched_queue) {
    return;  // Already in queue
  }

  p->in_sched_queue = 1;

  // Update total weight
  int weight = get_weight(p->nice);
  total_weight += weight;

  // Set vruntime to prevent starvation
  // Improved strategy for fair scheduling and anti-starvation

  if (runqueue.size > 0) {
    // Get min vruntime from heap root
    uint64 min_vrt = runqueue.heap[0]->vruntime;

    if (p->vruntime == 0) {
      // New process: set to min vruntime to start fairly
      p->vruntime = min_vrt;
    } else {
      // Waking process: calculate appropriate vruntime based on sleep time
      uint64 sleep_time = p->sleep_time;
      uint64 max_lag = 100 * 1024;  // Maximum lag allowed (100 ticks * NICE_0_WEIGHT)

      if (p->vruntime + max_lag < min_vrt) {
        // Process has been sleeping too long, limit the lag to prevent starvation
        p->vruntime = min_vrt - max_lag;
      } else if (p->vruntime < min_vrt) {
        // Give a small bonus for sleeping, but not too much
        uint64 sleep_bonus = (sleep_time < 50) ? sleep_time * 10 : 500;
        if (min_vrt > sleep_bonus) {
          p->vruntime = min_vrt - sleep_bonus;
        } else {
          p->vruntime = 0;
        }
      }
      // If p->vruntime >= min_vrt, keep it as is (no bonus needed)
    }
  } else {
    // First process in queue
    if (p->vruntime == 0) {
      p->vruntime = 0;  // Start from 0 for first process
    }
  }

  // Insert into heap
  heap_insert(p);
}

// Remove process from scheduling queue
// Must be called with both sched_lock and p->lock held
static void remove_from_sched_queue(struct proc *p) {
  if (!p->in_sched_queue) {
    return;  // Not in queue
  }

  p->in_sched_queue = 0;

  // Update total weight
  int weight = get_weight(p->nice);
  total_weight -= weight;

  // Remove from heap
  heap_remove(p);
}

// Lock ordering rules:
// 1. Always acquire p->lock before sched_lock to avoid deadlock
// 2. Never hold both tickslock and sched_lock simultaneously
// 3. When acquiring multiple process locks, use consistent ordering (by address)
// 4. Always check process state after acquiring locks due to potential races
//
// you must hold p->lock to call this function
void update_state(struct proc *p, enum procstate newstate) {
  // Validate that we hold the process lock
  if (!holding(&p->lock)) {
    panic("update_state: must hold p->lock");
  }

  // Read current time without holding tickslock to avoid deadlock
  // This is safe because ticks is only incremented, never decremented
  uint current_time = ticks;

  // Calculate time spent in the old state
  uint time_elapsed = current_time - p->last_timestamp;

  // Update time counters based on old state
  enum procstate oldstate = p->state;
  if (oldstate == RUNNING) {
    p->running_time += time_elapsed;
    // Update vruntime: vruntime += time_elapsed * NICE_0_WEIGHT / weight
    // This ensures processes with higher priority (lower nice) get more CPU time
    int weight = get_weight(p->nice);
    p->vruntime += (time_elapsed * 1024) / weight;  // 1024 is NICE_0_WEIGHT
  } else if (oldstate == RUNNABLE) {
    p->runnable_time += time_elapsed;
  } else if (oldstate == SLEEPING) {
    p->sleep_time += time_elapsed;
  }

  // Update state and timestamp
  p->state = newstate;
  p->last_timestamp = current_time;

  // Update scheduling queue based on state transitions
  // Follow lock ordering: p->lock is already held, now acquire sched_lock
  acquire(&sched_lock);

  if (oldstate == RUNNABLE && newstate != RUNNABLE) {
    // Leaving RUNNABLE state, remove from queue
    remove_from_sched_queue(p);
  } else if (oldstate != RUNNABLE && newstate == RUNNABLE) {
    // Entering RUNNABLE state, add to queue
    add_to_sched_queue(p);
  }

  release(&sched_lock);
}

// ============================================================================
// Min-heap implementation for CFS scheduling queue
// ============================================================================

// Swap two elements in the heap
// Must be called with sched_lock held
static void heap_swap(int i, int j) {
  if (i >= runqueue.size || j >= runqueue.size) return;

  struct proc *temp = runqueue.heap[i];
  runqueue.heap[i] = runqueue.heap[j];
  runqueue.heap[j] = temp;
}

// Bubble up element at index to maintain heap property
// Must be called with sched_lock held
static void heap_bubble_up(int index) {
  if (index == 0) return;

  int parent = (index - 1) / 2;
  if (runqueue.heap[index]->vruntime < runqueue.heap[parent]->vruntime) {
    heap_swap(index, parent);
    heap_bubble_up(parent);
  }
}

// Bubble down element at index to maintain heap property
// Must be called with sched_lock held
static void heap_bubble_down(int index) {
  int left = 2 * index + 1;
  int right = 2 * index + 2;
  int smallest = index;

  if (left < runqueue.size &&
      runqueue.heap[left]->vruntime < runqueue.heap[smallest]->vruntime) {
    smallest = left;
  }

  if (right < runqueue.size &&
      runqueue.heap[right]->vruntime < runqueue.heap[smallest]->vruntime) {
    smallest = right;
  }

  if (smallest != index) {
    heap_swap(index, smallest);
    heap_bubble_down(smallest);
  }
}

// Insert a process into the heap
// Must be called with sched_lock held
static void heap_insert(struct proc *p) {
  if (!holding(&sched_lock)) {
    panic("heap_insert: must hold sched_lock");
  }

  if (runqueue.size >= SCHED_QUEUE_SIZE) {
    panic("heap_insert: queue full");
  }

  runqueue.heap[runqueue.size] = p;
  heap_bubble_up(runqueue.size);
  runqueue.size++;
}

// Extract the minimum element (root) from the heap
// Must be called with sched_lock held
static struct proc *heap_extract_min(void) {
  if (!holding(&sched_lock)) {
    panic("heap_extract_min: must hold sched_lock");
  }

  if (runqueue.size == 0) {
    return 0;
  }

  struct proc *min_proc = runqueue.heap[0];
  runqueue.heap[0] = runqueue.heap[runqueue.size - 1];
  runqueue.size--;

  if (runqueue.size > 0) {
    heap_bubble_down(0);
  }

  return min_proc;
}

// Find the index of a process in the heap
// Must be called with sched_lock held
// Returns -1 if not found
static int heap_find(struct proc *p) {
  for (int i = 0; i < runqueue.size; i++) {
    if (runqueue.heap[i] == p) {
      return i;
    }
  }
  return -1;
}

// Remove a specific process from the heap
// Must be called with sched_lock held
static void heap_remove(struct proc *p) {
  int index = heap_find(p);
  if (index == -1) {
    return;  // Process not in heap
  }

  // Replace with last element
  runqueue.heap[index] = runqueue.heap[runqueue.size - 1];
  runqueue.size--;

  if (runqueue.size > 0 && index < runqueue.size) {
    // Restore heap property
    uint64 parent_vruntime = (index > 0) ? runqueue.heap[(index-1)/2]->vruntime : 0;
    uint64 current_vruntime = runqueue.heap[index]->vruntime;

    if (index > 0 && current_vruntime < parent_vruntime) {
      heap_bubble_up(index);
    } else {
      heap_bubble_down(index);
    }
  }
}
