#include "types.h"
#include "riscv.h"
#include "defs.h"
#include "date.h"
#include "param.h"
#include "memlayout.h"
#include "spinlock.h"
#include "proc.h"

uint64 sys_exit(void) {
  int n;
  if (argint(0, &n) < 0) return -1;
  exit(n);
  return 0;  // not reached
}

uint64 sys_getpid(void) { return myproc()->pid; }

uint64 sys_fork(void) { return fork(); }

uint64 sys_wait(void) {
  uint64 p;
  if (argaddr(0, &p) < 0) return -1;
  return wait(p);
}

uint64 sys_sbrk(void) {
  int addr;
  int n;

  if (argint(0, &n) < 0) return -1;
  addr = myproc()->sz;
  if (growproc(n) < 0) return -1;
  return addr;
}

uint64 sys_sleep(void) {
  int n;
  uint ticks0;

  if (argint(0, &n) < 0) return -1;
  acquire(&tickslock);
  ticks0 = ticks;
  while (ticks - ticks0 < n) {
    if (myproc()->killed) {
      release(&tickslock);
      return -1;
    }
    sleep(&ticks, &tickslock);
  }
  release(&tickslock);
  return 0;
}

uint64 sys_kill(void) {
  int pid;

  if (argint(0, &pid) < 0) return -1;
  return kill(pid);
}

// return how many clock tick interrupts have occurred
// since start.
uint64 sys_uptime(void) {
  uint xticks;

  acquire(&tickslock);
  xticks = ticks;
  release(&tickslock);
  return xticks;
}

uint64 sys_rename(void) {
  char name[16];
  int len = argstr(0, name, MAXPATH);
  if (len < 0) {
    return -1;
  }
  struct proc *p = myproc();
  memmove(p->name, name, len);
  p->name[len] = '\0';
  return 0;
}

uint64 sys_pstate(void) {
  int pid;
  uint64 running_time_addr, runnable_time_addr, sleep_time_addr;

  // Get arguments
  if (argint(0, &pid) < 0) return -1;
  if (argaddr(1, &running_time_addr) < 0) return -1;
  if (argaddr(2, &runnable_time_addr) < 0) return -1;
  if (argaddr(3, &sleep_time_addr) < 0) return -1;

  // Find the process with the given pid
  struct proc *p;
  for (p = proc; p < &proc[NPROC]; p++) {
    acquire(&p->lock);
    if (p->pid == pid) {
      // Read current time while holding the lock to ensure consistency
      // This is safe because ticks is only incremented atomically
      uint current_time = ticks;

      uint running_time = p->running_time;
      uint runnable_time = p->runnable_time;
      uint sleep_time = p->sleep_time;

      // Add time in current state
      uint time_elapsed = current_time - p->last_timestamp;
      if (p->state == RUNNING) {
        running_time += time_elapsed;
      } else if (p->state == RUNNABLE) {
        runnable_time += time_elapsed;
      } else if (p->state == SLEEPING) {
        sleep_time += time_elapsed;
      }

      release(&p->lock);

      // Copy out the results
      if (copyout(myproc()->pagetable, running_time_addr, (char *)&running_time, sizeof(uint)) < 0)
        return -1;
      if (copyout(myproc()->pagetable, runnable_time_addr, (char *)&runnable_time, sizeof(uint)) < 0)
        return -1;
      if (copyout(myproc()->pagetable, sleep_time_addr, (char *)&sleep_time, sizeof(uint)) < 0)
        return -1;

      return 0;
    }
    release(&p->lock);
  }

  // Process not found
  return -1;
}

uint64 sys_cpustate(void) {
  uint64 cpu_time_addr;

  // Get argument
  if (argaddr(0, &cpu_time_addr) < 0) return -1;

  // Copy CPU times to user space
  uint cpu_times[NCPU];
  for (int i = 0; i < NCPU; i++) {
    cpu_times[i] = cpus[i].cpu_time;
  }

  if (copyout(myproc()->pagetable, cpu_time_addr, (char *)cpu_times, sizeof(cpu_times)) < 0)
    return -1;

  return 0;
}

uint64 sys_setnice(void) {
  int nice;

  // Get argument
  if (argint(0, &nice) < 0) return -1;

  // Validate nice value (must be 1, 2, or 3)
  if (nice < 1 || nice > 3) {
    return -1;
  }

  struct proc *p = myproc();
  acquire(&p->lock);

  // Update nice value
  int old_nice = p->nice;
  p->nice = nice;

  // If process is in scheduling queue, update total weight
  if (p->in_sched_queue) {
    extern struct spinlock sched_lock;
    extern uint total_weight;

    acquire(&sched_lock);
    // Remove old weight and add new weight
    int old_weight = 4 - old_nice;
    int new_weight = 4 - nice;
    total_weight = total_weight - old_weight + new_weight;
    release(&sched_lock);
  }

  release(&p->lock);

  return 0;
}
