#!/usr/bin/python3

import re
import subprocess
from gradelib import *

r = Runner(save("xv6.out"))

import re
from gradelib import *

r = Runner(save("xv6.out"))

@test(5, "trace 32 grep")
def test_trace_32_grep():
    r.run_qemu(shell_script([
        'trace 32 grep hello README'
    ]))
    r.match('^\\d+: sys_read\\(3\\) -> 1023')
    r.match('^\\d+: sys_read\\(3\\) -> 966')
    r.match('^\\d+: sys_read\\(3\\) -> 0')

@test(5, "trace all grep")
def test_trace_all_grep():
    r.run_qemu(shell_script([
        'trace 2147483647 grep hello README'
    ]))
    r.match('^\\d+: sys_trace\\(2147483647\\) -> 0')
    r.match('^\\d+: sys_exec\\(12240\\) -> 3')
    r.match('^\\d+: sys_open\\(12240\\) -> 3')
    r.match('^\\d+: sys_read\\(3\\) -> 1023')
    r.match('^\\d+: sys_read\\(3\\) -> 966')
    r.match('^\\d+: sys_read\\(3\\) -> 0')
    r.match('^\\d+: sys_close\\(3\\) -> 0')

@test(5, "trace nothing")
def test_trace_nothing():
    r.run_qemu(shell_script([
        'grep hello README'
    ]))
    r.match(no=[".* syscall .*"])

@test(5, "trace children")
def test_trace_children():
    r.run_qemu(shell_script([
        'trace 2 usertests forkforkfork'
    ]))
    r.match('3: sys_fork\\([-\\+]?\\d+\\) -> 4')
    r.match('^5: sys_fork\\([-\\+]?\\d+\\) -> \\d+')
    r.match('^6: sys_fork\\([-\\+]?\\d+\\) -> \\d+')
    r.match('^\\d+: sys_fork\\([-\\+]?\\d+\\) -> -1')
    r.match('^ALL TESTS PASSED')

@test(14, "sysinfotest")
def test_sysinfotest():
    r.run_qemu(shell_script([
        'sysinfotest'
    ]))
    r.match('^sysinfotest: OK', no=[".* FAIL .*"])


run_tests()



